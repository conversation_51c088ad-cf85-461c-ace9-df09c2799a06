import { useQuery } from "@tanstack/react-query";
import { AuthResponse } from "@/types/auth";
import { getAccountInfo } from "@/services/resources/auth";
import { getAuthResponse } from "@/utils/cookie-manager";

export function useAuthResponse() {
  console.log("🔐 useAuthResponse called");
  const data = getAuthResponse<AuthResponse>();
  console.log("🔐 Auth response data:", data);

  // Additional debugging information
  if (data) {
    console.log(
      "🔐 Auth response exists - selected role:",
      data["selected-role"]
    );
    console.log("🔐 Auth response roles available:", data.roles?.length || 0);
  } else {
    console.log("🔐 No auth response found in cookies");
  }

  return { data };
}

export function useAccountInfo() {
  return useQuery({
    queryKey: ["account-info"],
    queryFn: getAccountInfo,
  });
}
